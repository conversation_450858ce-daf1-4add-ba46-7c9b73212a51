import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'

// https://vite.dev/config/
export default defineConfig(({ mode }) => {
  const isProduction = mode === 'production'

  return {
    plugins: [
      react()
    ],

    // Configure dev server for better HMR in Tauri
    server: {
      // Let Vite find an available port
      port: 3000,
      // Don't use strict port - let it find next available
      strictPort: false,
      // Configure host for Tauri
      host: '127.0.0.1',
      // Enable HMR with better configuration for Tauri
      hmr: {
        // Use polling for file changes in Tauri environment
        overlay: true,
        // Force HMR to use the same host
        host: '127.0.0.1',
      },
      // Watch options for better file change detection
      watch: {
        // Use polling for better file detection in Tauri
        usePolling: true,
        interval: 1000,
        // Ignore node_modules for better performance
        ignored: ['**/node_modules/**', '**/target/**'],
      },
    },

    // Clear screen on file changes
    clearScreen: false,

    // Environment variables for Tauri
    envPrefix: ['VITE_', 'TAURI_'],

    // Build configuration
    build: {
      // Generate source maps for better debugging
      sourcemap: !isProduction,
      // Optimize for Tauri
      target: 'esnext',
      minify: isProduction ? 'esbuild' : false,
      // Configure multiple entry points
      rollupOptions: {
        input: {
          main: './index.html',
          'screen-view': './screen-view.html',
          'screen-view-tauri': './screen-view-tauri.html'
        }
      }
    },

    // Optimize dependencies
    optimizeDeps: {
      // Include common dependencies
      include: ['react', 'react-dom', '@iconify/react'],
      // Force pre-bundling of iconify
      force: true,
    },

    // Define global constants for Tauri environment
    define: {
      // Help Iconify detect Tauri environment
      __TAURI__: JSON.stringify(true),
    },
  }
})
